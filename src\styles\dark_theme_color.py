from .style import Style


class DarkThemeColor:
    class Color:
        # COMMON
        primary = '#75FE8C'
        text = "#F7F0F7"
        text_same_bg = "#000000"
        text_pressed = "#75f48c"
        text_yellow = "#F6BE00"
        test = "#00ff0d"
        button_background_normal = "#656475"
        button_background_chosen = "#5B5B9F"
        border_color = "#656475"
        hover_color = "#363636"  # Darker version of widget_background_1 for better contrast
        login_dialog_background = "rgba(16, 24, 40, 0.5)"  # Nền mờ 50%

        # Video Control Colors
        video_control_button_normal = "#656475"
        video_control_button_active = "#5B5B9F"
        video_control_button_hover = "#7A7A8F"
        video_control_text_normal = "#F7F0F7"
        video_control_text_active = "#FFFFFF"
        video_control_text_hover = "#FFFFFF"  # White for better visibility on dark theme
        video_control_background = "#2B2A3A"
        video_control_border = "#656475"
        video_control_speed_normal = "#656475"
        video_control_speed_active = "#5B5B9F"
        video_control_speed_hover = "#7A7A8F"
        video_control_speed_active_hover = "#3F3F87"

        # Live Button Colors
        live_button_background = "#2C2C2C"  # Dark gray background
        live_button_text = "#F7F0F7"        # Light text for contrast
        live_button_hover = "#3C3C3C"       # Slightly lighter on hover
        live_button_active = "#B5122E"      # Red for active state
        live_button_active_text = "#FFFFFF" # White text on active state
        live_indicator_active = "#16BD1D"   # Green for active indicator
        live_indicator_inactive = "#B5122E" # Red for inactive indicator

        # Status Colors
        error_color = "#ED4845"
        warning_color = "#FF915E"

        # Volume Controls
        volume_button_background = "#2C2C2C"  # Dark gray background
        volume_button_hover = "#3C3C3C"      # Lighter on hover
        volume_slider_background = "#3C3C3C"  # Dark gray background
        volume_slider_handle = "#5B5B9F"     # Primary color for handle
        volume_slider_handle_border = "#FFFFFF" # White border
        volume_slider_handle_hover = "#3F3F87" # Darker primary on hover
        volume_slider_progress = "#5B5B9F"    # Primary color for progress

        # Mode Switch
        mode_switch_background = "#2C2C2C"   # Dark gray background
        mode_switch_text = "#F7F0F7"         # Light text
        mode_switch_hover = "#3C3C3C"        # Lighter on hover
        mode_switch_active = "#B5122E"       # Red for active state
        mode_switch_active_text = "#FFFFFF"  # White text on active

        # Calendar Button
        calendar_button_background = "#2C2C2C" # Dark gray background
        calendar_button_hover = "#3C3C3C"     # Lighter on hover

        # Loading and Buffering
        success_color = "#1CD1A1"
        loading_background = "rgba(43, 42, 58, 0.8)"
        loading_border = "#5B5B9F"
        loading_progress_background = "#656475"
        loading_progress_chunk = "#5B5B9F"
        buffering_background = "rgba(43, 42, 58, 0.5)"  # 50% transparent version of camera_widget_background
        buffering_background_80 = "rgba(43, 42, 58, 0.8)"
        button_text_normal = "#F7F0F7"

        label_title_1 = "#F7F0F7"

        widget_background_1 = "#2B2A3A"
        widget_background_2 = "#3A3A3A"
        
        dialog_header_background = "#656475"
        dialog_body_background = "#0F1123"
        dialog_button_background = "#656475"
        dialog_text = "#F7F0F7"
        dialog_border = "#656475"  # Using common border color
        
        input_border = "#656475"
        input_background = "#3A3A3A"
        input_text = "#F7F0F7"

        tabbar_background_normal = "#0F1123"
        tabbar_background_selected = "rgba(117, 254, 140, 0.2)"
        tabbar_text_normal = "#FFFFFF"
        tabbar_text_selected = "#FFFFFF"

        subtabbar_text_normal = "#F7F0F7"

        treeview_item_background_selected = "#656475"
        treeview_on_hover = "#656475"

        sidetab_border = "#3a372a"
        main_border = "#303241"
        main_background = "#0F1123"
        main_background_splitter = "#0F1123"
        bubble_background = "#2D5842"

        # SERVER ITEM
        server_item_background_off = "transparent"
        server_item_background_active = "#23463b"
        server_item_background_inactive = "transparent"
        server_item_background_hoverred = "#363546"
        server_item_title = "#ffffff"
        server_item_border_hoverred = "#F7F0F7"
        server_item_edit_button_background_hoverred = "#0F1123"

        # FILTER COLOR
        filter_background = "#1A1B32"
        
        # ADD SERVER WIDGET
        add_server_widget_background = "#2B2A3A"
        add_server_sub_title = "#F7F0F7"
        add_server_main_title = "#F7F0F7"
        add_server_lineedit_focused = "#F7F0F7"

        # SEARCH BAR
        search_server_background = "#2B2A3A"
        search_server_lineedit_background = "#363546"
        search_server_lineedit_border = "#656475"
        search_server_lineedit_placeholder = "#656475"
        search_server_border = "transparent"

        # TITLE BAR
        titlebar_close_hoverred = "#5B5B9F"

        # TABLE WIDGET
        table_row_hoverred = "#1A1B30"
        table_row_text = "#F7F0F7"
        # table_odd_row_background = "#FFFFFF"
        # table_even_row_background = "#FFFFFF"
        table_action_background_hoverred = "#2B2A3A"
        table_header_background = "#2F3042"
        table_header_text = "#F7F0F7"
        table_page_indicator_background = "#0F1123"
        table_page_indicator_text = "#656475"
        table_pagination_background = "#2B2A3A"
        table_pagination_text = "#656475"
        table_pagination_background_selected = "#5B5B9F"
        table_pagination_text_selected = "white"

        table_normal_button_background = "#656475"
        table_normal_button_background_hoverred = "#5D5C6D"
        table_normal_button_text = "#E3E3E3"

        table_item_header_background = "#656475"
        table_item_header_name_text = "#E8E0E8"
        table_item_header_text = "#F7F0F7"

        table_edit_item_background = "#363546"
        table_edit_item_background_hoverred = "#656475"

        

        # CAMERA WIDGET
        camera_widget_background = '#2B2A3A'
        camera_widget_background_clicked = "#5B5B9F"
        camera_widget_text_clicked = '#5B5B9F'


        # text color
        text_color_all_app = '#F5F5F5'
        text_disable = '#656475'
        text_on_primary = '#F7F0F7'

        # COMBOBOX
        combobox_item_active_background = "#2E2E2E"
        combobox_item_inactive_background = "#2B2A3A"

        # HOVER COLOR
        hover_button = '#2B2A3A'
        dialog_close_hover_bg = "#656475"  # Using hover color from dark theme

        # DIVIDER
        divider = '#656475'
        common_border = '#2F2E41'
        tabbar_border = '#2F2E41'

        # NODE GRAPHIC
        r_node_graphic_background = 23
        g_node_graphic_background = 23
        b_node_graphic_background = 23
        # DRAW AI ZONE
        ai_zone_background = "#363546"

        # AI BUTTON STATUS COLOR
        ai_checkbox_background_disabled = "#3A3A3A"
        ai_checkbox_background_off = "#656475"
        ai_checkbox_background_on = "#5B5B9F"
        ai_checkbox_text_enabled = "#FFFFFF"
        ai_checkbox_text_disabled = "#FFFFFF"

        ai_button_background_off = "#656475"
        ai_button_background_on = "#5B5B9F"
        ai_button_text_on = "#FFFFFF"
        ai_button_text_off = "#FFFFFF"


        file_button_focus = "#2B2A3A"
        # TimeLineController
        timeline_background_disabled = "#0F1123"
        timeline_background_off = "#1A1B32"
        timeline_background_on = "#2C4844"
        timeline_border_off = "#333544"
        timeline_icon = "#FFFFFF"
        timeline_text_off = "#FFFFFF"
        timeline_text_on = "#FFFFFF"
        daytime_background_on = "#F6BE00"
        ###############################
        white = "#FFFFFF"
        black = "#000000"
        default = "efefef"
        # primary = "#5B5B9F"
        green = '#1CD1A1'
        primary = '#75FE8C'
        primary_hover = "#3F3F87"
        primary_pressed = "#3F3F87"
        secondary = "#2B2A3A"
        background = "#0F1123"
        on_background = "#2B2A3A"
        on_hover = "#656475"
        on_hover_primary = "#3F3F87"
        on_hover_secondary = "#656475"
        on_hover_button = "#3F3F87"
        hover_button_toolbar = "#656475"
        background_item = "#363546"
        background_item_off = "#2F2E3C"
        # Text color
        white = "#FFFFFF"
        text_black = "#2B2A3A"
        text_selected = "#FFFFFF"
        text_unselected = "#656475"
        white_2 = "#F7F0F7"
        text_title_color = "#FFFFFF"
        text_place_holder = "#656475"
        text_disable = "#656475"
        disable_color = "#656475"
        text_on_primary = '#EEEEEE'
        text_not_select = "#F7F0F7"
        text_note = '#575757'
        # border and stroke
        border_line_edit = "#F7F0F7"
        border_item = "#979797"
        # divider = "#656475"
        border_line_edit_not_focus = "#656475"
        # Button
        button_second_background = "#656475"
        button_primary_background = "#5B5B9F"
        button_disable_background = "#242424"
        # Menu
        menu_title = "#A5A5A5"

        available = "#48DC6B"
        unavailable = "#CC5051"
        status_appear = "rgba(61, 173, 254, 1)"
        status_checkin = "rgba(19, 172, 25, 1)"
        status_checkout = "rgba(204, 80, 81, 1)"
        text_camera_name = "rgba(255, 255, 255, 0.7)"
        # divider = "rgba(42, 43, 50, 1)"
        background_search_bar_event = "rgba(2, 2, 3, 0.45)"
        text_search_bar_event = "rgba(255, 255, 255, 0.7)"
        status_appear_dialog = "#41A0FA"
        # event_number_color ='rgba(255, 145, 94, 1)'
        event_number_color = "#FF915E"
        button_color = "#3F3F87"
        button_disable = "#3A3A3A"
        border_button = "#5C687F"
        error = "#B5122E"
        pulse_toggle_color = "#FFFFFF"
        background_warning = "#363546"
        transparent = "rgba(0, 0, 0, 0)"
        button_upload = "#3388DC"
        background_dialog = "#2B2A3A"
        background_box = "#B5122E"
        background_box_hover = "#8F0F25"
        server_connected = "#1CD1A1"
        widget_disable = "3A3A3A"
        
        # Calendar colors
        calendar_selection_text = "#FFFFFF"
        calendar_selection_bg = "#5B5B9F"
        calendar_background = "#2B2A3A"
        calendar_text = "#F7F0F7"
        calendar_border = "#656475"
        calendar_menu_bg = "#2B2A3A"
        calendar_menu_text = "#F7F0F7"
        
        # Time picker colors
        time_picker_bg = "#2B2A3A"
        time_picker_text = "#F7F0F7"
        time_picker_border = "#656475"
        time_picker_border_hover = "#5B5B9F"

        # GridItem
        grid_item_hover_border = "#75FE8C"
        grid_item_drag_border = "#FF6B6B"

    class Image:
        ##############################
        # CUSTOM MAIN TAB BAR
        server_item_edit_icon = ":src/assets/login_screen/edit_server_dark.svg"
        server_tab_off = ":src/assets/side_menu_icon/server_screen_off_dark.svg"
        server_tab_on = ":src/assets/side_menu_icon/server_screen_on_dark.svg"

        streaming_tab_not_select = ":src/assets/side_menu_icon/streaming_screen_off_dark.svg"
        streaming_tab_select = ":src/assets/side_menu_icon/streaming_screen_on_dark.svg"

        device_tab_not_select = ":src/assets/side_menu_icon/device_screen_off_dark.svg"
        device_tab_select = ":src/assets/side_menu_icon/device_screen_on_dark.svg"
        
        user_tab_select = ":src/assets/side_menu_icon/user_screen_on_dark.svg"
        user_tab_not_select = ":src/assets/side_menu_icon/user_screen_off_dark.svg"
        
        setting_tab_select = ":src/assets/side_menu_icon/setting_screen_on_dark.svg"
        setting_tab_not_select = ":src/assets/side_menu_icon/setting_screen_off_dark.svg"
        logo_login = ":src/assets/login_screen/logo_login_dark.svg"
        logo_login_mini = ":src/assets/login_screen/logo_login_dark_mini.svg"

        # TITLE BAR ICON
        titlebar_close_application_normal = Style.PrimaryImage.close_application_white
        titlebar_minimize_window_normal = Style.PrimaryImage.minimize_window_white
        titlebar_maximize_window_normal = Style.PrimaryImage.maximize_window_white
        titlebar_settings_application_normal = Style.PrimaryImage.settings_application_white
        titlebar_normal_window_normal = Style.PrimaryImage.normal_window_white

        # CHECKBOX
        checkbox_checked_qml = "qrc:src/assets/tool_icons/checkbox_checked_qml.svg"
        checkbox_checked = ":src/assets/tool_icons/checkbox_checked_dark.svg"
        checkbox_unchecked = ":src/assets/tool_icons/checkbox_unchecked_dark.svg"
        checkbox_partially_checked = ":src/assets/tool_icons/checkbox_partially_checked_dark.svg"

        table_eye = ":/src/assets/images/eye_in_table_dark.svg"
        table_edit = ":/src/assets/images/edit_in_table_dark.svg"
        table_trash = ":src/assets/tool_icons/trash_dark.svg"

        refresh = ":src/assets/event/refresh_dark.svg"
        delete = ":/src/assets/images/delete_icon_dark.svg"

        dialog_close = ":src/assets/tool_icons/close_dialog_dark.svg"
        dialog_close_hoverred = ":src/assets/tool_icons/close_dialog_dark.svg"
        # TREE VIEW
        treeview_expand_item = ":src/assets/treeview_and_menu_treeview/expand_treeview_dark.svg"
        treeview_collapse_item = ":src/assets/treeview_and_menu_treeview/collapse_treeview_dark.svg"
        search = ":src/assets/treeview_and_menu_treeview/search_loop_dark.svg"
        group_camera_treeview = ":src/assets/treeview_and_menu_treeview/group_camera_treeview_dark.svg"
        treeview_server = ":src/assets/treeview_and_menu_treeview/treeview_server_dark.svg"
        list_devices = ":src/assets/treeview_and_menu_treeview/list_devices_dark.svg"
        list_map = ":src/assets/treeview_and_menu_treeview/list_map_dark.svg"
        list_virtual_window = ":src/assets/treeview_and_menu_treeview/list_virtual_windows_dark.svg"
        list_save_view = ":src/assets/treeview_and_menu_treeview/list_save_view_dark.svg"
        close_all_virtual = ":/src/assets/treeview_and_menu_treeview/close_all_virtual_dark.svg"
        open_all_virtual = ":/src/assets/treeview_and_menu_treeview/open_all_virtual_dark.svg"
        building_on = ":/src/assets/map/building_on.svg"
        building_off = ":/src/assets/map/building_off_dark.svg"
        change_mode = ":src/assets/treeview_and_menu_treeview/change_mode_dark.svg"

        # MAP
        icon_background_map = "qrc:src/assets/map/icon_arc_background_dark.svg"

        # GRID
        grid_1x1 = ":src/assets/grid/grid_1x1_dark.svg"
        grid_2x2 = ":src/assets/grid/grid_2x2_dark.svg"
        grid_3x3 = ":src/assets/grid/grid_3x3_dark.svg"
        grid_4x4 = ":src/assets/grid/grid_4x4_dark.svg"
        grid_5x5 = ":src/assets/grid/grid_5x5_dark.svg"
        grid_6x6 = ":src/assets/grid/grid_6x6_dark.svg"
        grid_8x8 = ":src/assets/grid/grid_8x8_dark.svg"
        custom_8_grid = ":src/assets/grid/custom_8_grid_dark.svg"
        custom_6_grid = ":src/assets/grid/custom_6_grid_dark.svg"
        custom_10_grid = ":src/assets/grid/custom_10_grid_dark.svg"
        custom_13_grid = ":src/assets/grid/custom_13_grid_dark.svg"
        edit_layout_grid = ":src/assets/grid/edit_layout_grid_dark.svg"
        # BOTTOM TOOL BAR
        grid_off = ":src/assets/bottom_toolbar/grid_off_dark.svg"
        stream_flow_off = ":src/assets/bottom_toolbar/stream_flow_off_dark.svg"
        stream_flow_2_off = ":src/assets/bottom_toolbar/stream_flow_2_off_dark.svg"
        stream_flow_3_off = ":src/assets/bottom_toolbar/stream_flow_3_off_dark.svg"
        exit_stream_off = ":src/assets/bottom_toolbar/exit_stream_off_dark.svg"

        # SPLITTER
        icon_sidebar_big_left = ":src/assets/splitter/splitter_arrow_left_dark.svg"
        icon_status_sidebar = ":src/assets/splitter/icon_status_sidebar_dark.svg"
        icon_sidebar_big_right = ":src/assets/splitter/splitter_arrow_right_dark.svg"
        sidebar_filter = ":src/assets/side_menu_icon/filter_dark.svg"

        # EVENT BAR
        lightning_on = ":src/assets/event/lightning_on_dark.svg"
        lightning_off = ":src/assets/event/lightning_off_dark.svg"
        alert_off = ":src/assets/event/alert_off_dark.svg"
        alert_on = ":src/assets/event/alert_on_dark.svg"
        calendar = ":src/assets/event/calendar_dark.svg"
        ic_filter = ":src/assets/event/filter_dark.svg"

        # TAB BAR CAMERA SCREEN
        close_tab = ':src/assets/tab_icon/close_tab_dark.svg'
        close_filter = ':src/assets/images/close_filter_dark.svg'
        
        add_tab = ":src/assets/images/add_tab_dark.svg"
        add_button_icon = ":src/assets/images/add_tab_light.svg"

        # LOGIN SCREEN
        user = ":src/assets/login_screen/user_dark.svg"
        lock = ":src/assets/login_screen/lock_dark.svg"
        search_server = ":src/assets/login_screen/search_server_dark.svg"
        icon_server_name = ":src/assets/login_screen/icon_server_name.svg"
        icon_state_server_on = ":src/assets/login_screen/icon_state_server_on.svg"
        icon_state_server_off = ":src/assets/login_screen/icon_state_server_off.svg"
        # IMAGES
        no_data_image = ":src/assets/images/no_data_image_dark.svg"
        draw_polygon_option = ":src/assets/images/polygon_option_dark.svg"
        upload_file = ":/src/assets/images/upload_dark.svg"
        download_file = ":src/assets/images/download_dark.svg"
        down_arrow = "qrc:src/assets/images/down_arrow_dark.svg"
        up_arrow = "qrc:src/assets/images/up_arrow_dark.svg"
        down_arrow_linedit = ":src/assets/arrows/down_arrow_lineedit.svg"
        
        # TOOL ICONS
        checkbox_ver2_checked = "qrc:/src/assets/tool_icons/checkbox_ver2_checked_dark.svg"
        checkbox_ver2_unchecked = "qrc:/src/assets/tool_icons/checkbox_ver2_unchecked.svg"

        # CAMERA STREAM PTZ ICON
        camera_item = "qrc:src/assets/camera_stream/camera_item.svg"
        building_item = "qrc:src/assets/camera_stream/building_item.svg"
        icon_volume = ":src/assets/camera_stream/icon_volume.svg"
        icon_ptz_off = ":src/assets/camera_stream/icon_ptz_off.svg"
        expand_camera = "qrc:src/assets/camera_stream/expand_camera.svg"
        shrink_camera = "qrc:src/assets/camera_stream/shrink_camera.svg"
        icon_record = ":src/assets/camera_stream/icon_record.svg"
        icon_close = "qrc:src/assets/camera_stream/icon_close.svg"
        icon_crop_off = ":src/assets/camera_stream/icon_crop_off.svg"
        icon_drag_zoom_off = ":src/assets/camera_stream/icon_drag_zoom_off.svg"
        icon_drag_zoom = "qrc:src/assets/camera_stream/icon_drag_zoom.svg"
        ptz_arrow = ":src/assets/camera_stream/ptz_arrow.svg"
        icon_ptz_arrow_off = ":src/assets/camera_stream/icon_ptz_arrow_off.svg"
        icon_ptz_arrow = "qrc:src/assets/camera_stream/icon_ptz_arrow.svg"
        icon_ptz = "qrc:src/assets/camera_stream/icon_ptz.svg"
        rotate_camera = "qrc:src/assets/camera_stream/rotate_camera.svg"
        # CONTEXT MENU
        icon_ai_flow = "qrc:src/assets/context_menu/icon_ai_flow.svg"
        icon_full_screen = "qrc:src/assets/context_menu/icon_full_screen.svg"
        icon_open_cam = "qrc:src/assets/context_menu/icon_open_cam.svg"
        icon_setting = "qrc:src/assets/context_menu/icon_setting.svg"
        icon_video_stream = "qrc:src/assets/context_menu/icon_video_stream.svg"
        icon_remove_cam = "qrc:src/assets/context_menu/icon_remove_cam.svg"

        # PTZ ICON
        left_top = ":src/assets/ptz_icon/left_top.svg"
        top = ":src/assets/ptz_icon/top.svg"
        right_top = ":src/assets/ptz_icon/right_top.svg"
        left = ":src/assets/ptz_icon/left.svg"
        around = ":src/assets/ptz_icon/around.svg"
        right = ":src/assets/ptz_icon/right.svg"
        left_bottom = ":src/assets/ptz_icon/left_bottom.svg"
        bottom = ":src/assets/ptz_icon/bottom.svg"
        right_bottom = ":src/assets/ptz_icon/right_bottom.svg"
        zoom_in = ":src/assets/ptz_icon/zoom_in.svg"
        focus_near = ":src/assets/ptz_icon/focus_near.svg"
        iris_add = ":src/assets/ptz_icon/iris_plus.svg"
        zoom_out = ":src/assets/ptz_icon/zoom_out.svg"
        focus_far = ":src/assets/ptz_icon/focus_far.svg"
        iris_not_add = ":src/assets/ptz_icon/iris_minus.svg"
        down_speed = ":src/assets/ptz_icon/down_speed.svg"
        up_speed = ":src/assets/ptz_icon/up_speed.svg"

        drop_dow = ":src/assets/ptz_icon/dropdown.svg"
        drop_dow_right = ":src/assets/ptz_icon/drop_down_right.svg"

        # PRESET PATRON
        call_preset = ":src/assets/preset_patrol/call_preset.svg"
        setting_preset = ":src/assets/preset_patrol/setting_preset.svg"
        delete_preset = ":src/assets/preset_patrol/delete_preset.svg"
        play_patrol = ":src/assets/preset_patrol/play_patrol.svg"
        stop_patrol = ":src/assets/preset_patrol/stop_patrol.svg"
        preset = ":src/assets/preset_patrol/preset.svg"
        patrol = ":src/assets/preset_patrol/patrol.svg"
        pattern = ":src/assets/preset_patrol/pattern.svg"
        ptz_advance_brightness = ":src/assets/preset_patrol/ptz_advance_brightness.svg"
        ptz_advance_contrast = ":src/assets/preset_patrol/ptz_advance_contrast.svg"
        ptz_advance_sharpness = ":src/assets/preset_patrol/ptz_advance_sharpness.svg"
        ptz_advance_saturation = ":src/assets/preset_patrol/ptz_advance_saturation.svg"
        ptz_advance_menu = ":src/assets/preset_patrol/ptz_advance_menu.svg"

        # SPINBOX
        down_spinbox_temp = "qrc:src/assets/tool_icons/down_spinbox_dark.svg"
        up_spinbox_temp = "qrc:src/assets/tool_icons/up_spinbox_dark.svg"
        copy_schedule_to = "qrc:src/assets/tool_icons/copy_schedule_to_icon.svg"
                # PLAYBACK
        next_chunk = "qrc:src/assets/playback/next_chunk_dark.svg"
        next_frame = "qrc:src/assets/playback/next_frame_dark.svg"
        new_pause = "qrc:src/assets/playback/pause_dark.svg"
        new_play = "qrc:src/assets/playback/play_dark.svg"
        previous_chunk = "qrc:src/assets/playback/previous_chunk_dark.svg"
        previous_frame = "qrc:src/assets/playback/previous_frame_dark.svg"
        record_state_on = "qrc:src/assets/playback/record_state_on.svg"
        record_state_off = "qrc:src/assets/playback/record_state_off.svg"
        ##############################
        cursor = ":src/assets/images/cursor_arrow.cur"
        cursor_busy = ":src/assets/images/cursor_busy.gif"
        cursor_horizontal_resize = ":src/assets/images/cursor_horizontal.cur"
        cursor_vertical_resize = ":src/assets/images/cursor_vertical.cur"
        cursor_top_left_corner = ":src/assets/images/cursor_tlbr.cur"
        cursor_top_right_corner = ":src/assets/images/cursor_bltr.cur"
        cursor_link = ":src/assets/images/cursor_link.cur"
        cursor_help = ":src/assets/images/cursor_help.cur"
        cursor_move = ":src/assets/images/cursor_move.cur"
        add = ":/src/assets/images/plus.svg"

        add_camera_address = ":/src/assets/images/add_camera_address.png"
        delete_icon = ":/src/assets/images/delete_icon.svg"
        delete_icon_disable = ":/src/assets/images/delete_icon_disable.svg"
        delete_camera_address = ":/src/assets/images/delete_camera_address.png"
        delete_ai_script = ":src/assets/images/delete_ai_script.svg"
        show_hide = ":/src/assets/images/show_hide.png"
        close_stream = ":/src/assets/images/close_stream.png"
        sucess_result = ":/src/assets/images/sucess_result.png"
        fail_result = ":/src/assets/images/fail_result.png"
        info_result = ":/src/assets/images/info_result.png"
        close_notify = ":/src/assets/images/close_notify.png"
        edit_virtual = ":/src/assets/images/edit_virtual.png"
        remove_all_virtual = ":/src/assets/images/remove_all_virtual.png"
        add_virtual = ":/src/assets/images/add_virtual.png"
        icon64 = ":/src/assets/images/icon_64.png"
        icon48 = ":/src/assets/images/icon_48.png"
        icon32 = ":/src/assets/images/icon_32.png"
        icon128 = ":/src/assets/images/icon_128.png"
        restart_path = ":/src/assets/images/restart.png"

        ic_camera_connecting = ":src/assets/state/camera_connecting.png"
        ic_camera_disconnect = ":src/assets/state/camera_disconnect.svg"
        pause_path = ":src/assets/images/camera_pause.png"

        server = ":/src/assets/images/server.png"

        fast_forward = ":/src/assets/images/fast_forward.png"
        fast_backward = ":/src/assets/images/fast_backward.png"
        play_video = ":/src/assets/images/play_video.png"
        stop_video = ":/src/assets/images/stop_video.png"
        pause = ":/src/assets/images/pause.png"
        play = ":/src/assets/images/play.png"
        speed_video = ":/src/assets/images/speed_video.png"
        sound_video = ":/src/assets/images/sound_video.png"
        mute_sound_video = ":/src/assets/images/mute_sound_video.png"
        minimize_video = ":/src/assets/images/minimize_video.png"
        fullscreen_video = ":/src/assets/images/fullscreen_video.png"
        volume_down_fill = ":/src/assets/images/volume_down_fill.svg"
        volume_mute_fill = ":/src/assets/images/volume_mute_fill.svg"
        volume_up_fill = ":/src/assets/images/volume_up_fill.svg"

        edit = ":src/assets/images/edit.png"
        double_right = ":src/assets/images/double_right.png"
        plus = ":src/common/widget/feature_controller/images/plus.svg"
        record = ":src/assets/images/record.png"
        edit_map = ":src/assets/map/edit_map.png"
        rotate_map = ":src/assets/map/rotate_map.png"
        zoomin_map = ":src/assets/map/zoomin_map.png"
        zoomout_map = ":src/assets/map/zoomout_map.png"
        
        expand_camera_on_map = "qrc:src/assets/map/expand_stream_dark.svg"
        collapse_camera_on_map = "qrc:src/assets/map/collapse_stream_dark.svg"
        close_camera_on_map = "qrc:src/assets/map/close_stream_dark.svg"

        setting = ":src/assets/images/setting.png"
        show = ":src/assets/images/show.png"
        expand_right = ":src/assets/images/expand_right.svg"
        expand_bottom = ":src/assets/images/expand_bottom.svg"
        audio = ":src/assets/images/audio.png"
        ptz = ":src/assets/images/ptz.png"
        playback = ":src/assets/images/playback.png"
        zoom_digital = ":src/assets/images/zoom_digital.png"
        stop_live = ":src/assets/images/stop_live.png"
        info = ":src/assets/images/info.png"
        icon_status_timeline = ":src/assets/splitter/icon_status_timeline_dark.svg"
        calendar_icon = ":/src/assets/images/icon_calendar.svg"

        vehicle_detection_on = ":src/assets/ai_icons/vehicle_detection_on.svg"
        vehicle_detection_ver2 = ":src/assets/ai_icons/vehicle_detection_ver2.svg"
        vehicle_detection_off = ":src/assets/ai_icons/vehicle_detection_off.svg"
        face_recognition_on = ":src/assets/ai_icons/face_recognition_on.svg"
        face_recognition_off = ":src/assets/ai_icons/face_recognition_off.svg"
        face_recognition_ver2 = ":src/assets/ai_icons/face_recognition_ver2.svg"
        crowd_detection_on = ":src/assets/ai_icons/crowd_detection_on.svg"
        crowd_detection_off = ":src/assets/ai_icons/crowd_detection_off.svg"
        access_control_on = ":src/assets/ai_icons/access_control_on.svg"
        access_control_off = ":src/assets/ai_icons/access_control_off.svg"

        ic_recognition_security = ":src/assets/ai_icons/ic_recognition_security.svg"
        ic_risk_identification = ":src/assets/ai_icons/ic_risk_identification.svg"

        apply = ":src/assets/images/apply.png"
        speed = ":src/assets/images/speed.png"
        share = ":src/assets/images/share.png"
        cut = ":src/assets/images/cut.png"
        recording_icon_header = ":src/assets/images/recording.png"
        # icon_volume = ":src/assets/camera_stream/icon_volume.svg"
        # icon_ptz_on = ":src/assets/camera_stream/icon_ptz_on.svg"
        # icon_ptz_off = ":src/assets/camera_stream/icon_ptz_off.svg"
        # expand_camera = ":src/assets/camera_stream/expand_camera.svg"
        # shrink_camera = ":src/assets/camera_stream/shrink_camera.svg"
        # icon_record = ":src/assets/camera_stream/icon_record.svg"
        # icon_close = ":src/assets/camera_stream/icon_close.svg"
        # icon_crop_off = ":src/assets/camera_stream/icon_crop_off.svg"
        # icon_crop_on = ":src/assets/camera_stream/icon_crop_on.svg"
        # icon_drag_zoom_off = ":src/assets/camera_stream/icon_drag_zoom_off.svg"
        # icon_drag_zoom_on = ":src/assets/camera_stream/icon_drag_zoom_on.svg"
        # ptz_arrow = ":src/assets/camera_stream/ptz_arrow.svg"
        # icon_ptz_arrow_off = ":src/assets/camera_stream/icon_ptz_arrow_off.svg"
        # icon_ptz_arrow_on = ":src/assets/camera_stream/icon_ptz_arrow_on.svg"

        # lightning_on = ":src/assets/event/lightning_on.svg"
        # lightning_off = ":src/assets/event/lightning_off.svg"
        # alert_off = ":src/assets/event/alert_off.svg"
        # alert_on = ":src/assets/event/alert_on.svg"
        # calendar = ":src/assets/event/calendar.svg"

        playback_tab_select = ":src/assets/side_menu_icon/playback_screen_on.svg"
        map_tab_select = ":src/assets/side_menu_icon/map_screen_on.svg"
        playback_tab_not_select = ":src/assets/side_menu_icon/playback_screen_off.svg"
        map_tab_not_select = ":src/assets/side_menu_icon/map_screen_off.svg"
        logo_tab_bar = ":src/assets/side_menu_icon/logo_tab_bar.svg"

        mic_disable = ":src/assets/images/mic_disable.png"
        record_off = ":src/assets/images/record_off.png"
        record_disable = ":src/assets/images/record_disable.png"
        record_on = ":src/assets/images/record_on.png"
        capture_disable = ":src/assets/images/capture_disable.png"
        capture_off = ":src/assets/images/capture_off.png"
        fullscreen_off = ":src/assets/images/fullscreen_off.png"
        volume_disable = ":src/assets/images/volume_disable.png"
        volume_off = ":src/assets/images/volume_off.png"
        volume_on = ":src/assets/images/volume_on.png"
        window_size_off = ":src/assets/images/window_size_off.png"
        # close_tab = ":src/assets/images/close_tab.png"
        colume_option = ":src/assets/images/colume_option.png"
        state_online = ":src/assets/state/state_online.svg"
        state_offline = ":src/assets/state/state_offline.svg"

        # tool icons
        checkbox_unchecked_white = ":src/assets/tool_icons/checkbox_unchecked_white.svg"
        trash = ":src/assets/tool_icons/trash.svg"
        trash_ver2 = ":src/assets/tool_icons/trash_ver2.svg"
        trash_mini = ":src/assets/tool_icons/mini_trash.svg"
        pen = ":src/assets/tool_icons/edit.svg"
        pen_ver2 = ":src/assets/tool_icons/pen_ver2.svg"

        # tree view icon, menu in treeview icon
        camera_active_icon_red = ":src/assets/treeview_and_menu_treeview/camera_active_icon_red.svg"
        camera_active_icon_green = ":src/assets/treeview_and_menu_treeview/camera_active_icon_green.svg"
        camera_active_icon_green_qml = "qrc:/src/assets/treeview_and_menu_treeview/camera_active_icon_green.svg"
        norec_pin = ":src/assets/treeview_and_menu_treeview/norec_pin.svg"
        connected_norec_unpin = ":src/assets/treeview_and_menu_treeview/connected_norec_unpin.svg"
        rec_pin = ":src/assets/treeview_and_menu_treeview/rec_pin.svg"
        rec_unpin = ":src/assets/treeview_and_menu_treeview/rec_unpin.svg"
        norec_pin = ":src/assets/treeview_and_menu_treeview/norec_pin.svg"
        disconnected_norec_unpin = ":src/assets/treeview_and_menu_treeview/disconnected_norec_unpin.svg"
        rec_pin = ":src/assets/treeview_and_menu_treeview/rec_pin.svg"
        disrec_unpin = ":src/assets/treeview_and_menu_treeview/disrec_unpin.svg"
        norec_pin_qml = "qrc:/src/assets/treeview_and_menu_treeview/norec_pin.svg"
        connected_norec_unpin_qml = "qrc:/src/assets/treeview_and_menu_treeview/connected_norec_unpin.svg"
        rec_pin_qml = "qrc:/src/assets/treeview_and_menu_treeview/rec_pin.svg"
        rec_unpin_qml = "qrc:/src/assets/treeview_and_menu_treeview/rec_unpin.svg"
        norec_pin_qml = "qrc:/src/assets/treeview_and_menu_treeview/norec_pin.svg"
        disconnected_norec_unpin_qml = "qrc:/src/assets/treeview_and_menu_treeview/disconnected_norec_unpin.svg"
        rec_pin_qml = "qrc:/src/assets/treeview_and_menu_treeview/rec_pin.svg"
        disrec_unpin_qml = "qrc:/src/assets/treeview_and_menu_treeview/disrec_unpin.svg"
        camera_inactive_icon = ":src/assets/treeview_and_menu_treeview/camera_in_active_treeview.svg"
        choose_position_icon = ":src/assets/treeview_and_menu_treeview/choose_position_camera_treeview.png"
        exit_stream_treeview = ":src/assets/treeview_and_menu_treeview/exit_stream_treeview.png"
        # group_camera_treeview = ":src/assets/treeview_and_menu_treeview/group_camera_treeview.svg"
        # open_in_tab = ":src/assets/treeview_and_menu_treeview/open_in_tab_treeview.png"
        # treeview_server = ":src/assets/treeview_and_menu_treeview/treeview_server.svg"
        # list_devices = ":src/assets/treeview_and_menu_treeview/list_devices.svg"
        # list_map = ":src/assets/treeview_and_menu_treeview/list_map.svg"
        # list_virtual_window = ":src/assets/treeview_and_menu_treeview/list_virtual_windows.svg"
        # list_save_view = ":src/assets/treeview_and_menu_treeview/list_save_view.svg"
        # close_all_virtual = ":/src/assets/treeview_and_menu_treeview/close_all_virtual.svg"
        # open_all_virtual = ":/src/assets/treeview_and_menu_treeview/open_all_virtual.svg"
        previous_first = ":src/assets/images/first_page.svg"
        previous = ":src/assets/images/previous_page.svg"
        next = ":src/assets/images/next_page.svg"
        next_last = ":src/assets/images/latest_page.svg"
        expand_item_treeview = ":src/assets/treeview_and_menu_treeview/expand_treeview_dark.svg"
        collapse_item_treeview = ":src/assets/treeview_and_menu_treeview/collapse_treeview_dark.svg"
        new_search = ":src/assets/treeview_and_menu_treeview/new_search.svg"
        Filter = ":src/assets/treeview_and_menu_treeview/Filter.png"
        Change_mode = ":src/assets/treeview_and_menu_treeview/change_mode.svg"
        ai_menu_filter = ":src/assets/treeview_and_menu_treeview/ai_menu_filter.png"
        complex_tree = ":src/assets/treeview_and_menu_treeview/complex_tree.png"
        state_menu_filter = ":src/assets/treeview_and_menu_treeview/state_menu_filter.png"

        # PTZ icon
        left_top = ":src/assets/ptz_icon/left_top.svg"
        top = ":src/assets/ptz_icon/top.svg"
        right_top = ":src/assets/ptz_icon/right_top.svg"
        left = ":src/assets/ptz_icon/left.svg"
        around = ":src/assets/ptz_icon/around.svg"
        right = ":src/assets/ptz_icon/right.svg"
        left_bottom = ":src/assets/ptz_icon/left_bottom.svg"
        bottom = ":src/assets/ptz_icon/bottom.svg"
        right_bottom = ":src/assets/ptz_icon/right_bottom.svg"
        zoom_in = ":src/assets/ptz_icon/zoom_in.svg"
        focus_near = ":src/assets/ptz_icon/focus_near.svg"
        iris_add = ":src/assets/ptz_icon/iris_plus.svg"
        zoom_out = ":src/assets/ptz_icon/zoom_out.svg"
        focus_far = ":src/assets/ptz_icon/focus_far.svg"
        iris_not_add = ":src/assets/ptz_icon/iris_minus.svg"
        drop_dow = ":src/assets/ptz_icon/dropdown.svg"
        drop_dow_right = ":src/assets/ptz_icon/drop_down_right.svg"
        close_dialog = ":src/assets/tool_icons/close_dialog_white.svg"
        # Login screen
        frame_login = ":src/assets/login_screen/frame_login.png"
        login_background = ":src/assets/login_screen/login_background.png"
        add_server = ":src/assets/login_screen/add_server.svg"
        noti_server = ":src/assets/login_screen/noti_server.svg"
        logo = ":src/assets/login_screen/logo.svg"
        logo_video = ":src/assets/login_screen/login_video.gif"
        logovideobackground = ":src/assets/login_screen/loginvideobackground.gif"
        edit_server = ":src/assets/login_screen/edit_server.png"
        edit_server = ":src/assets/login_screen/edit_server.png"
        eye_close = ":src/assets/login_screen/eye_close.png"
        eye = ":src/assets/login_screen/eye.png"
        # preset --------------
        call_preset = ":src/assets/preset_patrol/call_preset.svg"
        setting_preset = ":src/assets/preset_patrol/setting_preset.svg"
        delete_preset = ":src/assets/preset_patrol/delete_preset.svg"
        play_patrol = ":src/assets/preset_patrol/play_patrol.svg"
        stop_patrol = ":src/assets/preset_patrol/stop_patrol.svg"
        preset = ":src/assets/preset_patrol/preset.svg"
        patrol = ":src/assets/preset_patrol/patrol.svg"
        pattern = ":src/assets/preset_patrol/pattern.svg"
        add_preset = ":src/assets/preset_patrol/add_preset.svg"
        delete_preset_patrol = ":src/assets/preset_patrol/delete_preset_patrol.svg"
        down_preset = ":src/assets/preset_patrol/down_preset.svg"
        up_preset = ":src/assets/preset_patrol/up_preset.svg"
        call_preset_hover = ":src/assets/preset_patrol/call_preset_hover.svg"
        setting_preset_hover = ":src/assets/preset_patrol/setting_preset_hover.svg"
        delete_preset_hover = ":src/assets/preset_patrol/delete_preset_hover.svg"
        play_patrol_hover = ":src/assets/preset_patrol/play_patrol_hover.svg"
        stop_patrol_hover = ":src/assets/preset_patrol/stop_patrol_hover.svg"
        ptz_advance_brightness = ":src/assets/preset_patrol/ptz_advance_brightness.svg"
        ptz_advance_contrast = ":src/assets/preset_patrol/ptz_advance_contrast.svg"
        ptz_advance_sharpness = ":src/assets/preset_patrol/ptz_advance_sharpness.svg"
        ptz_advance_saturation = ":src/assets/preset_patrol/ptz_advance_saturation.svg"
        ptz_advance_menu = ":src/assets/preset_patrol/ptz_advance_menu.svg"

        down_speed = ":src/assets/ptz_icon/down_speed.svg"
        up_speed = ":src/assets/ptz_icon/up_speed.svg"
        # tab
        icon_active = ":src/assets/tab_icon/icon_active.png"
        exit_fullscreen = ":src/assets/images/exit_fullscreen.png"
        exit_fullscreen1 = ":src/assets/images/exit_fullscreen1.png"
        # draw detect area
        draw_pencil = ":src/assets/images/draw_pencil.png"
        eraser = ":src/assets/images/eraser.png"
        draw_line_option = ":src/assets/images/line_option.svg"
        down_arrow_custom = ":src/assets/images/down_arrow_custom.png"
        # right bar: filter event, time filter
        # ic_filter = ":src/assets/event/filter.svg"
        ic_time_filter = ":src/assets/images/time_filter.png"
        play_alarm_sound = ":src/assets/images/play_sound.svg"
        disable_play_alarm_sound = ":src/assets/images/disable_play_alarm_sound.svg"
        warning = ":src/assets/images/warning.gif"
        close_alert = ":src/assets/images/close_alert.png"
        alert_event = ":src/assets/images/alert_event.png"
        state_read_warning = ":src/assets/images/state_read.svg"
        # Playback screen
        download_playback = ":src/assets/playback/download_playback.png"
        mute_fill_playback = ":src/assets/playback/mute_fill.png"
        next_fill_playback = ":src/assets/playback/next_fill.png"
        play_fill_playback = ":src/assets/playback/play_fill.png"
        previous_fill_playback = ":src/assets/playback/previous_fill.png"
        volume_up_playback = ":src/assets/playback/volume_up.png"
        pause_fill_playback = ":src/assets/playback/pause_fill_playback.png"
        edit_zones = ":src/assets/images/edit_zones.png"
        ic_map = ":src/assets/treeview_and_menu_treeview/map_item.svg"
        delete_camera_on_map = ":src/assets/images/delete_camera_on_map.png"
        radio_button_checked = ":src/assets/images/radio_button_checked.png"
        radio_button_uncheck = ":src/assets/images/radio_button_uncheck.png"
        ic_ptz = ":src/assets/images/ic_ptz.png"
        ic_dome = ":src/assets/images/ic_dome.png"
        ic_bullet = ":src/assets/images/ic_bullet.png"
        ic_ptz_disable = ":src/assets/images/ic_ptz_disable.png"
        ic_dome_disable = ":src/assets/images/ic_dome_disable.png"
        ic_bullet_disable = ":src/assets/images/ic_bullet_disable.png"
        ic_image_not_found = ":src/assets/images/image_not_found.png"

        edit_map_name = ":src/assets/images/edit_map_name.png"
        edit_map_name_enable = ":src/assets/images/edit_map_name_enable.png"
        edit_map_camera = ":src/assets/images/edit_map_camera.png"
        edit_map_camera_enable = ":src/assets/images/edit_map_camera_enable.png"
        ahihi = ":src/assets/images/ahihi.jpg"
        face = ":src/assets/images/face.jpg"
        ic_bullet_svg = ":src/assets/images/ic_bullet.svg"
        ic_dome_svg = ":src/assets/images/ic_dome.svg"
        ic_ptz_svg = ":src/assets/images/ic_ptz.svg"
        # Side Menu active icon
        # icon_sidebar_big_left = ":src/assets/side_menu_icon/chevron_big_left.svg"
        # icon_status_sidebar = ":src/assets/side_menu_icon/icon_status_sidebar.svg"
        # icon_sidebar_big_right = ":src/assets/side_menu_icon/chevron_big_right.svg"

        # title bar
        close_application = ":src/assets/title_bar/close_window.svg"
        minimize_window = ":src/assets/title_bar/minimize_window.svg"
        maximize_window = ":src/assets/title_bar/maximize_window.svg"
        settings_application = ":src/assets/title_bar/settings.svg"
        normal_window = ":/src/assets/title_bar/normal_window.svg"
        close_application_dark = ":src/assets/title_bar/close_window_dark.svg"
        minimize_window_dark = ":src/assets/title_bar/minimize_window_dark.svg"
        maximize_window_dark = ":src/assets/title_bar/maximize_window_dark.svg"
        settings_application_dark = ":src/assets/title_bar/settings_dark.svg"
        normal_window_dark = ":/src/assets/title_bar/normal_window_dark.svg"

        ic_zone_activate = ":/src/assets/images/ic_zone_activate.png"
        edit_script = ":/src/assets/images/edit_script.png"
        alarm_alert_icon = ":/src/assets/images/alarm_alert.svg"
        camera_background_image = ":/src/assets/images/camera_background_image.svg"
        down_arrow_spinbox = ":src/assets/arrows/down_arrow_spinbox.svg"
        up_arrow_spinbox = ":src/assets/arrows/up_arrow_spinbox.svg"
        arrow_combobox = ":src/assets/arrows/arrow_combobox.svg"
        # Table
        eye_in_table = ":/src/assets/images/eye_in_table.svg"
        edit_in_table = ":/src/assets/images/edit_in_table.svg"
        # Dialog
        add_avatar = ":/src/assets/images/add_avatar.svg"
        clear_input = ":src/assets/images/clear_input.svg"
        loading_image = ":src/assets/images/loading_image.png"

        # Time picker icons
        time_picker_up = ":src/assets/playback/time_picker_up_dark.png"
        time_picker_down = ":src/assets/playback/time_picker_down_dark.png"
        search_not_found = ":src/assets/images/search_not_found_dark.svg"
        search_not_found_qml = "qrc:src/assets/images/search_not_found_dark.svg"

        # Device tab icons
        edit_device_item = "qrc:src/common/qml/device_table/images/pen_ver2_dark.svg"
        delete_device_item = "qrc:src/common/qml/device_table/images/trash_ver2_dark.svg"

    class Menu:
        stop = "src/assets/menu_icon/StopCircle.png"
        camera = "src/assets/menu_icon/Camera.png"
        rec = "src/assets/menu_icon/REC.png"
        ptz_control2 = "src/assets/menu_icon/PTZcontrol2.png"
        flag = "src/assets/menu_icon/Flag.png"
        playback = "src/assets/menu_icon/Playback.png"
        volume = "src/assets/menu_icon/VolumeUp.png"
        slider = "src/assets/menu_icon/Sliders.png"
        mic = "src/assets/menu_icon/Mic.png"
        ic_full_screen = "src/assets/menu_icon/ic_full_screen.png"
        close = "src/assets/menu_icon/Close.png"
