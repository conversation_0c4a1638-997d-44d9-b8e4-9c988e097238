import QtQuick 2.15
import QtQuick.Shapes

Rectangle{
    id: root
    width: 100
    height: 25
    color: "transparent"
    property color backgroundColor: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_color_theme_by_key("main_background") : "#F5F5F5"
    property color foregroundColor: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_color_theme_by_key("primary") : "red"
    property color bubbleFillColor: timeLineManager.timeLineController ? timeLineManager.timeLineController.get_color_theme_by_key("bubble_background") : "red"
    property string position: "0:00:00"
    property real pointOffset: 0

    Connections{
        target: timeLineManager.timeLineController
        function onThemeChanged() {
            root.backgroundColor = timeLineManager.timeLineController.get_color_theme_by_key("main_background")
            root.foregroundColor = timeLineManager.timeLineController.get_color_theme_by_key("primary")
            bubbleFillColor = timeLineManager.timeLineController.get_color_theme_by_key("bubble_background")
            shape.requestPaint()
        }
    }

    Rectangle{
        id: fill
        width: parent.width
        height: parent.height-shape.height
        anchors{
            bottom: shape.top
            left: parent.left
        }

        color: bubbleFillColor
        border{
            width: 1
            color: root.foregroundColor
        }
        radius: 12
    }

    // Rectangle{
    //     width: parent.width/2
    //     height: parent.height/2
    //     color: root.backgroundColor

    //     anchors{
    //         bottom: fill.bottom
    //         left: parent.left
    //     }
    // }

    Text {
        anchors{
            centerIn: fill
        }

        color: foregroundColor
        font: timeLineManager.timeLineController.positionFont

        text: position

    }

    Canvas {
        id: shape
        width: 4
        height: 4
        x : pointOffset
        anchors{
            bottom: parent.bottom
        }

        onPaint: {
            var context = getContext("2d");

            context.beginPath();
            context.moveTo(0,0);
            context.lineTo(2,4);
            context.lineTo(4,0);
            context.closePath();

            context.strokeStyle = root.foregroundColor;
            context.lineWidth = 2;
            context.stroke();

            context.fillStyle = root.foregroundColor;
            context.fill();

        }
    }
}