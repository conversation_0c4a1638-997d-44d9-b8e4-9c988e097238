import QtQuick
import QtQuick.Controls.Material 
import QtQuick.Layouts
import models 1.0

Item {
    id: root
    width: parent.width
    height: parent.height
    visible: true
    property var model
    property bool isPlayingStream: false
    property var itemName: ""
    property bool isHoverControl: false
    property bool isHoverVideo: false
    property bool isHovered: isHoverControl || isHoverVideo

    // Emit signal when hover state changes
    onIsHoveredChanged: {
        // console.log("isHovered changed:", isHovered)
        hoverStateChanged(isHovered)
    }
    signal closeSignal()
    signal fullScreenSignal()
    signal changeTabWidgetOpenSignal(bool open)
    signal hoverStateChanged(bool isHovering)
    z: 1001

    Component.onDestruction: {
        isPlayingStream = false
    }
    MapState {
        id: idMapState
    }
    
    HoverHandler {
        id: hoverHandler
        onHoveredChanged: {
            isHoverControl = hovered;
        }
    }

    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true
        propagateComposedEvents: true
        acceptedButtons: Qt.NoButton
        onEntered: {
            if (!hoverHandler.hovered) {
                isHoverControl = true;
            }
        }
        onExited: {
            if (!hoverHandler.hovered) {
                isHoverControl = false;
            }
        }
    }
    Rectangle{
        anchors.fill: parent
        color: backgroundColor
        topLeftRadius: 10
        topRightRadius: 10
        border.color: textColor
        border.width: 1

        Timer {
            id: updateTimer
            interval: 1000
            running: true
            repeat: true
            onTriggered: {
                let date = new Date();
                timeText.text = (date.getMonth() + 1) + "/" + date.getDate() + "/" + date.getFullYear() +
                                " " + date.getHours().toString().padStart(2, '0') + ":" +
                                date.getMinutes().toString().padStart(2, '0') + ":" +
                                date.getSeconds().toString().padStart(2, '0');
            }
        }
        ColumnLayout{
            anchors.fill: parent
            spacing: 0
            RowLayout{
                spacing: 0
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.preferredHeight: 35
                Item{
                    width: 5
                    height: 35
                }
                Image{
                    source: cameraItemIcon
                    width: 12
                    height: 12
                    fillMode: Image.PreserveAspectFit
                    sourceSize: Qt.size(width, height)
                    Layout.alignment: Qt.AlignLeft | Qt.AlignVCenter
                }
                Text{
                    text: itemName
                    color: textColor
                    elide: Text.ElideRight
                    wrapMode: Text.NoWrap
                    clip: true
                    font.bold: true
                    font.pixelSize: 12
                    Layout.preferredWidth: 120
                    Layout.leftMargin: 5
                    Layout.alignment: Qt.AlignLeft | Qt.AlignVCenter
                }
                Text {
                    id: timeText
                    text: "Loading..."
                    color: textColor
                    font.pixelSize: 12
                    Layout.leftMargin: 15
                    Layout.alignment: Qt.AlignLeft | Qt.AlignVCenter
                }
                Item { Layout.fillWidth: true }
            }

            CustomVideoOutput {
                width: 515
                height: 300
                Layout.fillHeight: true
                Layout.fillWidth: true
                Layout.margins: 2
                id: videoFrame
                isPlaying: isPlayingStream
                model: root.model
                MouseArea {
                    id: videoMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    propagateComposedEvents: true

                    onEntered: {
                        root.isHoverVideo = true;
                    }
                    onExited: {
                        root.isHoverVideo = false;
                    }
                }
            }
        }
    }
    

    property color backgroundColor: idMapState ? idMapState.get_color_theme_by_key("main_background") : "white"
    property color borderColor: idMapState ? idMapState.get_color_theme_by_key("main_border") : "white"
    property color textColor: idMapState ? idMapState.get_color_theme_by_key("text_color_all_app") : "white"
    property color primaryColor: idMapState ? idMapState.get_color_theme_by_key("primary") : "white"
    property string iconExpandCamera: idMapState ? idMapState.get_image_theme_by_key("expand_camera_on_map") : "white"
    property string iconCloseCamera: idMapState ? idMapState.get_image_theme_by_key("close_camera_on_map") : "white"
    property string iconCollapseCamera: idMapState ? idMapState.get_image_theme_by_key("collapse_camera_on_map") : "white"
    property string cameraItemIcon: idMapState ? idMapState.get_image_theme_by_key("camera_item") : "white"
    property string comboboxIcon: idMapState ? idMapState.get_image_theme_by_key("down_spinbox_temp") : "white"

    Connections{
        target: idMapState
        function onThemeChanged(){
            backgroundColor = idMapState.get_color_theme_by_key("main_background")
            borderColor = idMapState.get_color_theme_by_key("main_border")
            textColor = idMapState.get_color_theme_by_key("text_color_all_app")
            primaryColor = idMapState.get_color_theme_by_key("primary")
            cameraItemIcon = idMapState.get_image_theme_by_key("camera_item")
            iconExpandCamera = idMapState.get_image_theme_by_key("expand_camera_on_map")
            iconCollapseCamera = idMapState.get_image_theme_by_key("collapse_camera_on_map")
            iconCloseCamera = idMapState.get_image_theme_by_key("close_camera_on_map")
            comboboxIcon = idMapState.get_image_theme_by_key("down_spinbox_temp")
        }
    }
}

