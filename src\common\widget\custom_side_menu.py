from ast import main
from PySide6.QtWidgets import <PERSON>Widget, Q<PERSON>abel, QTabWidget, QVBoxLayout, QMenu, QTabBar,QApplication
from PySide6.QtGui import QPixmap, QImage, QTransform, QAction, QPainter, QPen
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtSvg import QSvgRenderer

from src.common.widget.custom_titlebar.actionable_title_bar import ActionableTitleBar
from src.styles.style import Style
from src.common.controller.main_controller import main_controller
from src.utils.config import Config
from src.common.joystick.joystick_manager import WidgetType,joystick_manager
import logging

from src.utils.theme_setting import theme_setting
logger = logging.getLogger(__name__)

class TabListWidget(QTabWidget):
    button_logout_signal = Signal(str)
    button_change_server_signal = Signal(str)

    def __init__(self, parent=None, list_Qwidget=None, window_parent=None):
        super().__init__()
        self.parent = parent
        self.window_parent=window_parent

        main_controller.list_parent['TabListWidget'] = self
        main_controller.complete_fetching_data.connect(self.complete_fetching_data)
        self.setObjectName("side-menu")
        
        self.list_Qwidget = list_Qwidget

        self.setTabPosition(QTabWidget.West)

        self.widget_logo = ActionableTitleBar(parent=self, window_parent=self.window_parent)
        self.image = QLabel()
        svg_renderer = QSvgRenderer(main_controller.get_theme_attribute("Image", "logo_login_mini"))
        pixmap = QPixmap(QSize(24, 24))
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        svg_renderer.render(painter)
        painter.end()
        self.image.setPixmap(pixmap)
        self.image.setAlignment(Qt.AlignCenter)
        layout_image = QVBoxLayout()
        layout_image.setContentsMargins(0, 0, 0, 0)
        layout_image.setSpacing(0)
        layout_image.addWidget(self.image, alignment=Qt.AlignCenter)
        wg_image = QWidget()
        wg_image.setObjectName("side-menu-logo")
        self.tabBar().setObjectName("side-menu-tabbar")
        wg_image.setLayout(layout_image)
        layout_logo = QVBoxLayout()
        layout_logo.setContentsMargins(0, 0, 0, 0)
        layout_logo.setSpacing(0)
        layout_logo.addWidget(wg_image)
        self.widget_logo.setLayout(layout_logo)
        self.widget_logo.setFixedSize(68, 41)
        self.widget_logo.setStyleSheet(
            f"""
            QWidget#side-menu-logo {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                border-bottom: 1px solid {main_controller.get_theme_attribute("Color", "sidetab_border")};
            }}
            """
        )
        self.setCornerWidget(self.widget_logo, Qt.Corner.TopLeftCorner)

        # Disable the drop-down button
        self.setUsesScrollButtons(False)

        self.addTab(self.list_Qwidget[0], "")
        # self.addTab(self.list_Qwidget[1], "")
        # self.addTab(self.list_Qwidget[2], "")
        # self.addTab(self.list_Qwidget[3], "")
        self.addTab(self.list_Qwidget[4], "")
        # self.addTab(QWidget(), "")

        image_paths_select = [
            main_controller.get_theme_attribute("Image", "server_tab_off"),
            main_controller.get_theme_attribute("Image", "streaming_tab_not_select"),
            main_controller.get_theme_attribute("Image", "device_tab_not_select"),
            main_controller.get_theme_attribute("Image", "user_tab_not_select"),
            main_controller.get_theme_attribute("Image", "setting_tab_not_select"),
            # main_controller.get_theme_attribute("Image", "Gear_tab_not_select"),

        ]
        image_paths_not_select = [
            main_controller.get_theme_attribute("Image", "server_tab_on"),
            main_controller.get_theme_attribute("Image", "streaming_tab_select"),
            main_controller.get_theme_attribute("Image", "device_tab_select"),
            main_controller.get_theme_attribute("Image", "user_tab_select"),
            main_controller.get_theme_attribute("Image", "setting_tab_select"),
            # Style.PrimaryImage.Gear_tab_select,

        ]
        self.rotated_icons_not_select = []
        self.rotated_icons_select = []
        self.is_full_tab = False
        self.process_tab_data()

        # Set the rotated icons as tab icons
        self.setTabIcon(0, self.rotated_icons_not_select[0])
        self.setTabIcon(1, self.rotated_icons_not_select[1])

        self.tab_names = {
            0: self.rotated_icons_select[0],
            1: self.rotated_icons_select[1],
        }
        # self.init_app()
        self.onTabChanged(0)
        self.currentChanged.connect(self.onTabChanged)
        self.setTabToolTip(0, Style.Toolstip.Server_Management)
        self.setTabToolTip(1, Style.Toolstip.Setting)

        self.tabBarClicked.connect(self.onTabBarClicked)
        # self.init_app()

    def add_tab_slot(self):
        self.is_full_tab = True
        self.process_tab_data()

        self.insertTab(1 ,self.list_Qwidget[1], "")
        self.insertTab(2, self.list_Qwidget[2], "")
        self.insertTab(3, self.list_Qwidget[3], "")
        self.setTabIcon(1, self.rotated_icons_not_select[1])
        self.setTabIcon(2, self.rotated_icons_not_select[2])
        self.setTabIcon(3, self.rotated_icons_not_select[3])
        self.setTabToolTip(1, Style.Toolstip.Streaming)
        self.setTabToolTip(2, Style.Toolstip.Device_Management)
        self.setTabToolTip(3, Style.Toolstip.User)
        self.tab_names = {
            0: self.rotated_icons_select[0],
            1: self.rotated_icons_select[1],
            2: self.rotated_icons_select[2],
            3: self.rotated_icons_select[3],
            4: self.rotated_icons_select[4],
        }

    def remove_all_tab_slot(self):
        self.is_full_tab = False
        self.process_tab_data()
        self.removeTab(3)
        self.removeTab(2)
        self.removeTab(1)
        self.tab_names = {
            0: self.rotated_icons_select[0],
            1: self.rotated_icons_select[1],
        }

    def complete_fetching_data(self,data):
        main_controller.signal_load_animation.emit(False)
        self.setCurrentIndex(1)

    def onTabBarClicked(self, index):
        if index == -1:
            pass
        else:
            self.setCurrentIndex(index)

    def onTabChanged(self, index):
        # Lấy tên tab hiện tại.
        current_tab_name = self.tabText(index)

        # Đặt lại tên ban đầu cho các tab không được chọn 
        for i in range(self.count()):
            if i != index:
                self.setTabIcon(i, self.tab_names[i])

        if index == 1:
            QApplication.instance().installEventFilter(main_controller.key_filter)
        else:
            QApplication.instance().removeEventFilter(main_controller.key_filter)
            
        if index == 0:
            new_tab_name = self.rotated_icons_not_select[0]
            joystick_manager.widget_type = WidgetType.ServerScreen
        elif index == 1:
            new_tab_name = self.rotated_icons_not_select[1]
            if self.count() == 2:
                joystick_manager.widget_type = WidgetType.SettingScreen
            else:
                joystick_manager.widget_type = WidgetType.CameraScreen
            # grid_item_selected.clear()
        elif index == 2:
            new_tab_name = self.rotated_icons_not_select[2]
            joystick_manager.widget_type = WidgetType.DeviceScreen
            # grid_item_selected.clear()
        elif index == 3:
            new_tab_name = self.rotated_icons_not_select[3]
            joystick_manager.widget_type = WidgetType.UserPermissionsScreen
            # grid_item_selected.clear()
        elif index == 4:
            new_tab_name = self.rotated_icons_not_select[4]
            joystick_manager.widget_type = WidgetType.SettingScreen
            # grid_item_selected.clear()
        else:
            new_tab_name = ""
            # grid_item_selected.clear()
            # Chỉ đổi tên nếu tên mới khác tên hiện tại
        if new_tab_name != current_tab_name:
            self.setTabIcon(index, new_tab_name)

    def change_tab_icon(self, index):
        if self.is_full_tab:
            self.tab_names = {
                0: self.rotated_icons_select[0],
                1: self.rotated_icons_select[1],
                2: self.rotated_icons_select[2],
                3: self.rotated_icons_select[3],
                4: self.rotated_icons_select[4],
            }
        else:
            self.tab_names = {
                0: self.rotated_icons_select[0],
                1: self.rotated_icons_select[1],
            }
            
        for i in range(self.count()):
            if i != index:
                self.setTabIcon(i, self.tab_names[i])

        new_tab_name = self.rotated_icons_not_select[index]
        current_tab_name = self.tabText(index)
        if new_tab_name != current_tab_name:
            self.setTabIcon(index, new_tab_name)

    def logout(self):
        print("logout")
        self.button_logout_signal.emit("Logout")

    def change_server(self):
        print("change server")
        self.button_change_server_signal.emit("Change Server")

    def process_tab_data(self):
        if self.is_full_tab:
            image_paths_select = [
                main_controller.get_theme_attribute("Image", "server_tab_off"),
                main_controller.get_theme_attribute("Image", "streaming_tab_not_select"),
                main_controller.get_theme_attribute("Image", "device_tab_not_select"),
                main_controller.get_theme_attribute("Image", "user_tab_not_select"),
                main_controller.get_theme_attribute("Image", "setting_tab_not_select"),
            ]
            image_paths_not_select = [
                main_controller.get_theme_attribute("Image", "server_tab_on"),
                main_controller.get_theme_attribute("Image", "streaming_tab_select"),
                main_controller.get_theme_attribute("Image", "device_tab_select"),
                main_controller.get_theme_attribute("Image", "user_tab_select"),
                main_controller.get_theme_attribute("Image", "setting_tab_select"),
                # Style.Image.Gear_tab_select,
            ]
        else:
            image_paths_select = [
                main_controller.get_theme_attribute("Image", "server_tab_off"),
                main_controller.get_theme_attribute("Image", "setting_tab_not_select"),
            ]
            image_paths_not_select = [
                main_controller.get_theme_attribute("Image", "server_tab_on"),
                main_controller.get_theme_attribute("Image", "setting_tab_select"),
            ]
        self.rotated_icons_not_select = []
        self.rotated_icons_select = []
        for path in image_paths_not_select:
            image = QImage(path)
            image_size = image.scaled(200, 200)
            transform = QTransform()
            transform.rotate(90)
            rotated_image = image_size.transformed(transform)
            pixmap = QPixmap.fromImage(rotated_image)
            self.rotated_icons_not_select.append(pixmap)
        for path in image_paths_select:
            image = QImage(path)
            image_size = image.scaled(200, 200)
            transform = QTransform()
            transform.rotate(90)
            rotated_image = image_size.transformed(transform)
            pixmap = QPixmap.fromImage(rotated_image)
            self.rotated_icons_select.append(pixmap)
    
    def paintEvent(self, event):
        super().paintEvent(event)
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Get the logo widget geometry
        logo_rect = self.widget_logo.geometry()
        
        # Draw bottom border for logo
        pen = QPen(main_controller.get_theme_attribute("Color", "tabbar_border"))
        pen.setWidth(2)
        painter.setPen(pen)
        painter.drawLine(logo_rect.left(), logo_rect.bottom(), 
                        logo_rect.right(), logo_rect.bottom())

        # Draw right border for tabbar, starting below the logo
        tabbar_rect = self.tabBar().geometry()
        painter.drawLine(tabbar_rect.right(), logo_rect.bottom(),
                        tabbar_rect.right(), self.rect().bottom())

        painter.end()

    def set_dynamic_stylesheet(self):
        svg_renderer = QSvgRenderer(main_controller.get_theme_attribute("Image", "logo_login_mini"))
        pixmap = QPixmap(QSize(24, 24))
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        svg_renderer.render(painter)
        painter.end()
        self.image.setPixmap(pixmap)
        self.setStyleSheet(
            f""" 
                QTabWidget::pane#side-menu {{
                    background-color: {main_controller.get_theme_attribute("Color", "tabbar_border")};
                }}
                QTabBar::tab#side-menu-tabbar {{
                    padding-bottom: 10px;
                    padding-left: 16px;
                    padding-right: 16px;
                    margin-bottom: 10px;
                    min-width: 28px;
                    border-left: 0px solid transparent;
                }}
                QTabBar::tab:selected#side-menu-tabbar {{
                    border-left: 3px solid {main_controller.get_theme_attribute("Color", "primary")};
                }}
                QTabBar::tab:first#side-menu-tabbar {{
                    padding-top: 0px;
                    margin-top: 16px;
                }}
                QTabWidget::tab-bar#side-menu-tabbar {{
                    alignment: left;
                }}
            """
        )

        self.widget_logo.setStyleSheet(
            f"""
            QWidget#side-menu-logo {{
                background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                border-bottom: 1px solid {main_controller.get_theme_attribute("Color", "sidetab_border")};
                min-width: 28px;
            }}
            """
        )
        self.process_tab_data()
        self.change_tab_icon(self.currentIndex())

    def resizeEvent(self, event):
        self.image.setFixedWidth(self.tabBar().sizeHint().width())
        self.widget_logo.setFixedWidth(self.tabBar().sizeHint().width())
        super().resizeEvent(event)

