from PySide6.QtCore import Qt
from PySide6.QtGui import QPixmap, QColor
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QGraphicsDropShadowEffect
from src.styles.style import Style
from src.common.controller.main_controller import main_controller

class LogoWidget(QWidget):
    def __init__(self,parent = None):
        super().__init__(parent)
        main_layout = QVBoxLayout(self)
        self.logo = self.create_logo_widget()
        main_layout.addWidget(self.logo)
        self.set_dynamic_stylesheet()

    def set_dynamic_stylesheet(self):
        self.setStyleSheet(f'''
            QWidget{{
                    background-color: transparent;
                    font-size: 20px;
                    font-weight: 200;}}
        ''')

        pixmap = QPixmap(main_controller.get_theme_attribute("Image", "logo_login"))
        self.label_image.setPixmap(pixmap)

    def create_logo_widget(self):
        pixmap = QPixmap(main_controller.get_theme_attribute("Image", "logo_login"))
        self.label_image = QLabel()
        self.label_image.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_image.setPixmap(pixmap)
        self.label_image.resize(pixmap.width(), pixmap.height())
        # shadow = QGraphicsDropShadowEffect(self.label_image)
        # shadow.setBlurRadius(32)
        # shadow.setColor(QColor(81, 212, 96, 180))
        # shadow.setOffset(0, 0)
        # self.label_image.setGraphicsEffect(shadow)

        widget = QWidget()
        logo_layout = QVBoxLayout(widget)
        logo_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # logo_layout.addStretch(4)
        logo_layout.addWidget(self.label_image)
        # logo_layout.addStretch(1)
        # logo_layout.addWidget(intro_label)
        # logo_layout.addStretch(1)
        return widget
   