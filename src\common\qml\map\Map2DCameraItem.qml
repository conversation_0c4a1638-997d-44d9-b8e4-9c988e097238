import QtQuick 
import QtQuick.Controls.Material
import QtQuick.Layouts
import './fovShape'

Item {
    id: _2dCameraItem
    property var _modelData: null
    property var _rootItem: null
    property var _area: null
    property int _modelIndex: -1

    property bool isPreviewHovered: false
    property var iconCoordinates: {
        "width": 0,
        "height": 0,
        "centerX": 0,
        "centerY": 0
    }

    property var mapState: {
        if (mapStateFromQML !== undefined)
            return mapStateFromQML;
        else if (map2dController)
            return map2dController.mapState;
        else
            return null;
    }

    property var mainGrid: {
        if(thisMapState && thisMapState.editMode) return null;
        var current = mapOnGrid.parent
        while (current) {
            if (current.objectName === "MainGrid" || current.toString().indexOf("MainGrid") !== -1) {
                return current
            }
            current = current.parent
        }
        return null
    }

    Timer {
        id: openPreviewTimer
        interval: 1000
        repeat: false
        onTriggered: {
            previewLoader.active = true;
            if (previewLoader.item) {
                previewLoader.item.isPlayingStream = true;
                updatePreviewPos(iconCoordinates.width,
                            iconCoordinates.height,
                            iconCoordinates.centerX,
                            iconCoordinates.centerY);
            }
        }
    }

    Timer {
        id: hoverTimer
        interval: 200
        repeat: false
        onTriggered: {
            // Chỉ tắt preview khi cả FOV lẫn preview đều không được hover
            if (previewLoader.item &&
                (!fovLoader.item || !fovLoader.item.isHovered) && !isPreviewHovered) {
                previewLoader.item.isPlayingStream = false;
                previewLoader.active = false;
            }
        }
    }


    on_ModelDataChanged: {
        if (_modelData) {
            if (_modelData.fovMode === "ICON") {
                fovLoader.sourceComponent = arcComponent;
            } else if (_modelData.fovMode === "RECTANGLE") {
                fovLoader.sourceComponent = rectangleComponent;
            } else if (_modelData.fovMode === "POLYGON") {
                fovLoader.sourceComponent = polygonComponent;
            } else if (_modelData.fovMode === "CIRCLE") {
                fovLoader.sourceComponent = circleComponent;
            } else {
                fovLoader.sourceComponent = null;
            }
        }
    }

    Loader {
        id: fovLoader
        onLoaded: {
            if (fovLoader.item)
                fovLoader.item.camData = _2dCameraItem._modelData;
        }
    }

    Component {
        id: rectangleComponent
        RectangleShape {
            _hoverArea: _area
            _mapState: mapState
            _item: _rootItem
            onClicked: {
                if (mapState && mapState.editMode) {
                    var itemCenter = this.mapToItem(_rootItem, centerX, centerY);
                    updateDialogPos(xSize,
                                    ySize,
                                    itemCenter.x,
                                    itemCenter.y);
                    configCameraFovLoader.item.visible = !configCameraFovLoader.item.visible;
                }
            }

            onCamDataUpdated: function(newData) {
                if (configCameraFovLoader.item) {
                    configCameraFovLoader.item.camData = newData;
                    _2dCameraItem._modelData = newData;
                    if (map2dController && _modelIndex >= 0) {
                        map2dController.modifyTempCameraList(_modelIndex, newData);
                    }
                }
            }

            onPosCoordChanged: function(){
                if (mapState && mapState.editMode) {
                    var itemCenter = this.mapToItem(_rootItem, centerX, centerY);
                    updateDialogPos(xSize,
                                    ySize,
                                    itemCenter.x,
                                    itemCenter.y);
                }
            }

            onIsHoveredChanged: function(){
                if (mapState && !mapState.editMode) {
                    if (isHovered) {
                        var itemCenter = this.mapToItem(_rootItem, centerX, centerY);
                        iconCoordinates = {
                            "width": xSize,
                            "height": ySize,
                            "centerX": itemCenter.x,
                            "centerY": itemCenter.y
                        }
                        hoverTimer.stop();
                        openPreviewTimer.start();
                    }
                    else{
                        openPreviewTimer.stop();
                        if (!isPreviewHovered) {
                            hoverTimer.start();
                        }
                    }
                }
            }
        }
    }

    Component {
        id: polygonComponent
        PolygonShape {
            _hoverArea: _area
            _mapState: mapState
            _item: _rootItem
            onClicked: {
                if (mapState && mapState.editMode) {
                    configCameraFovLoader.item.visible = !configCameraFovLoader.item.visible;
                }
            }
            
            onCamDataUpdated: function(newData) {
                if (configCameraFovLoader.item) {
                    configCameraFovLoader.item.camData = newData;
                    _2dCameraItem._modelData = newData;
                    if (map2dController && _modelIndex >= 0) {
                        map2dController.modifyTempCameraList(_modelIndex, newData);
                    }
                }
            }

            onBoundingRectChanged: function(){
                if (mapState && mapState.editMode && boundingRect) {
                    var itemCenter = this.mapToItem(_rootItem, boundingRect.centerX, boundingRect.centerY);
                    updateDialogPos(boundingRect.width,
                                    boundingRect.height,
                                    itemCenter.x,
                                    itemCenter.y);
                }
            }

            onReset: function(isReset){
                if (mapState && mapState.editMode) {
                    configCameraFovLoader.item.visible = !isReset;
                }
            }

            Component.onCompleted: {
                if (mapState && mapState.editMode) {
                    configCameraFovLoader.item.redrawButton.visible = true;
                }
            }

            Component.onDestruction: {
                if (mapState && mapState.editMode) {
                    configCameraFovLoader.item.redrawButton.visible = false;
                }
            }

            onIsHoveredChanged: function(){
                if (mapState && !mapState.editMode) {
                    if (isHovered && boundingRect) {
                        var itemCenter = this.mapToItem(_rootItem, boundingRect.centerX, boundingRect.centerY);
                        iconCoordinates = {
                            "width": boundingRect.width,
                            "height": boundingRect.height,
                            "centerX": itemCenter.x,
                            "centerY": itemCenter.y
                        }
                        hoverTimer.stop();
                        openPreviewTimer.start();
                    }
                    else{
                        openPreviewTimer.stop();
                        if (!isPreviewHovered) {
                            hoverTimer.start();
                        }
                    }
                }
            }
        }
    }

    Component {
        id: circleComponent
        CircleShape {
            _hoverArea: _area
            _mapState: mapState
            _item: _rootItem
            onClicked: {
                if (mapState && mapState.editMode) {
                    var itemCenter = this.mapToItem(_rootItem, centerX, centerY);
                    updateDialogPos(xSize,
                                    ySize,
                                    itemCenter.x,
                                    itemCenter.y);
                    configCameraFovLoader.item.visible = !configCameraFovLoader.item.visible;
                }
            }

            onCamDataUpdated: function(newData) {
                if (configCameraFovLoader.item) {
                    configCameraFovLoader.item.camData = newData;
                    _2dCameraItem._modelData = newData;
                    if (map2dController && _modelIndex >= 0) {
                        map2dController.modifyTempCameraList(_modelIndex, newData);
                    }
                }
            }

            onPosCoordChanged: function(){
                if (mapState && mapState.editMode) {
                    var itemCenter = this.mapToItem(_rootItem, centerX, centerY);
                    updateDialogPos(xSize,
                                    ySize,
                                    itemCenter.x,
                                    itemCenter.y);
                }
            }

            onIsHoveredChanged: function(){
                if (mapState && !mapState.editMode) {
                    if (isHovered) {
                        var itemCenter = this.mapToItem(_rootItem, centerX, centerY);
                        iconCoordinates = {
                            "width": xSize,
                            "height": ySize,
                            "centerX": itemCenter.x,
                            "centerY": itemCenter.y
                        }
                        hoverTimer.stop();
                        openPreviewTimer.start();
                    }
                    else{
                        openPreviewTimer.stop();
                        if (!isPreviewHovered) {
                            hoverTimer.start();
                        }
                    }
                }
            }
        }
    }

    Component {
        id: arcComponent
        IconArcShape {
            _hoverArea: _area
            _mapState: mapState
            _item: _rootItem
            onClicked: {
                if (mapState && mapState.editMode) {
                    var itemCenter = this.mapToItem(_rootItem, posCoord.x + iconItem.width/2, posCoord.y);
                    updateDialogPos(iconItem.width,
                                    iconItem.height,
                                    itemCenter.x,
                                    itemCenter.y);
                    configCameraFovLoader.item.visible = !configCameraFovLoader.item.visible;
                }
            }

            onCamDataUpdated: function(newData) {
                if (configCameraFovLoader.item) {
                    configCameraFovLoader.item.camData = newData;
                    _2dCameraItem._modelData = newData;
                    if (map2dController && _modelIndex >= 0) {
                        map2dController.modifyTempCameraList(_modelIndex, newData);
                    }
                }
            }

            onPosCoordChanged: function(){
                if (mapState && mapState.editMode) {
                    var itemCenter = this.mapToItem(_rootItem, posCoord.x + iconItem.width/2, posCoord.y);
                    updateDialogPos(iconItem.width,
                                    iconItem.height,
                                    itemCenter.x,
                                    itemCenter.y);
                }
            }

            onIsHoveredChanged: function(){
                if (mapState && !mapState.editMode) {
                    if (isHovered) {
                        var itemCenter = this.mapToItem(_rootItem, posCoord.x + iconItem.width/2, posCoord.y + iconItem.height/2);
                        iconCoordinates = {
                            "width": iconItem.width,
                            "height": iconItem.height,
                            "centerX": itemCenter.x,
                            "centerY": itemCenter.y
                        }
                        hoverTimer.stop();
                        openPreviewTimer.start();
                    }
                    else{
                        openPreviewTimer.stop();
                        if (!isPreviewHovered) {
                            hoverTimer.start();
                        }
                    }
                }
            }
        }
    }


    Loader {
        id: previewLoader
        active: false
        z: 1000
        parent: mainGrid ? mainGrid : _rootItem
        sourceComponent: PreviewItemMap2D {
            id: previewItem
            model: _modelData
            isPlayingStream: false
            visible: true
            itemName: _modelData ? _modelData.name : ""
            width: 500
            height: 340
            onCloseSignal: {
                isPlayingStream = false;
                previewLoader.active = false;
            }
            onFullScreenSignal: {
                if (mapState && mapState.editMode){
                    // handleDialogPositionEditMode(_2dCameraItem)
                }
            }
            onHoverStateChanged: function (isHovering) {
                isPreviewHovered = isHovering
                if(!isHovering){
                    hoverTimer.start()
                }
                else hoverTimer.stop()
            }
        }
    }

    
    Loader {
        id: configCameraFovLoader
        active: mapState ? mapState.editMode : false
        sourceComponent: ConfigCameraFovDialog {
            id: configCameraFovDialog
            visible: false
            parent: _2dCameraItem.parent
            camData: _modelData
            _mapState: mapState

            onChangeType: function(type) {
                var newFovData = getDefaultDataOfType(type);
                _2dCameraItem._modelData.fovData = JSON.stringify(newFovData);
                fovLoader.item.camData = _2dCameraItem._modelData;

                fovLoader.sourceComponent = type === "ICON" ? arcComponent :
                                            type === "RECTANGLE" ? rectangleComponent :
                                            type === "POLYGON" ? polygonComponent :
                                            type === "CIRCLE" ? circleComponent : arcComponent;

                if(type === "POLYGON"){
                    fovLoader.item.reset(true);
                }
            }

            onCamDataUpdated: function(newData) {
                if (fovLoader.item) {
                    fovLoader.item.camData = newData;
                    _2dCameraItem._modelData = newData;
                    if (map2dController && _modelIndex >= 0) {
                        map2dController.modifyTempCameraList(_modelIndex, newData);
                    }
                    
                    if (fovLoader.item.requestPaint) {
                        fovLoader.item.requestPaint();
                    }
                }
            }

            onDeleteCamera: function() {
                if (map2dController && _modelIndex >= 0) {
                    map2dController.temporarilyDeleteCamera(_modelIndex);
                }
            }

            onRedrawPolygon: function() {
                if (fovLoader.item) {
                    fovLoader.item.resetPoints();
                }
            }
        }
    }

    function updatePreviewPos(iconWidth, iconHeight, iconCenterX, iconCenterY) {
        var offset = 10;
        var dialog = previewLoader.item;
        if (!dialog) return;

        var dialogW = dialog.width;
        var dialogH = dialog.height;
        var mainGrid = _2dCameraItem.mainGrid;
        cón

        // Convert icon coordinates from _rootItem to mainGrid coordinate system
        var iconPosInMainGrid = _rootItem.mapToItem(mainGrid, iconCenterX, iconCenterY);
        var areaPosInMainGrid = _rootItem.mapToItem(mainGrid, _area.x, _area.y);
        var areaBottomRightInMainGrid = _rootItem.mapToItem(mainGrid, _area.x + _area.width, _area.y + _area.height);

        // biên của vùng cho phép trong mainGrid coordinate system
        var areaL = areaPosInMainGrid.x;
        var areaT = areaPosInMainGrid.y;
        var areaR = areaBottomRightInMainGrid.x;
        var areaB = areaBottomRightInMainGrid.y;

        // toạ độ cạnh của icon trong mainGrid coordinate system
        var iconCenterXInMainGrid = iconPosInMainGrid.x;
        var iconCenterYInMainGrid = iconPosInMainGrid.y;
        var iconL = iconCenterXInMainGrid - iconWidth/2;
        var iconR = iconCenterXInMainGrid + iconWidth/2;
        var iconT = iconCenterYInMainGrid - iconHeight/2;
        var iconB = iconCenterYInMainGrid + iconHeight/2;

        // vị trí mặc định: nằm giữa ngang, bên dưới icon
        var x = iconCenterXInMainGrid - dialogW/2;
        var y = iconB + offset;

        // nếu bên dưới không vừa, thử lên trên
        if (y + dialogH > areaB) {
            var yAbove = iconT - offset - dialogH;
            if (yAbove >= areaT) {
                y = yAbove;
            } else {
                // nếu cũng không vừa lên trên, thử bên phải
                var xRight = iconR + offset;
                if (xRight + dialogW <= areaR) {
                    x = xRight;
                    y = iconCenterYInMainGrid - dialogH/2;
                } else {
                    // thử bên trái
                    var xLeft = iconL - offset - dialogW;
                    if (xLeft >= areaL) {
                        x = xLeft;
                        y = iconCenterYInMainGrid - dialogH/2;
                    } else {
                        // fallback: clamp vào vùng
                        x = Math.min(Math.max(x, areaL), areaR - dialogW);
                        y = Math.min(Math.max(y, areaT), areaB - dialogH);
                    }
                }
            }
        }

        // luôn clamp để không vượt biên
        x = Math.min(Math.max(x, areaL), areaR - dialogW);
        y = Math.min(Math.max(y, areaT), areaB - dialogH);

        // gán toạ độ trong mainGrid coordinate system cho dialog
        dialog.x = x;
        dialog.y = y;
    }

    
    function getDefaultDataOfType(type) {
        var position = (_2dCameraItem._modelData.fovData ? JSON.parse(_2dCameraItem._modelData.fovData) : {}).position;
        var ratio = (_area ? (_area.scaleRatio ? _area.scaleRatio : 1) : 1);
        switch (type) {
            case "ICON":
                return {
                    'position': position,
                    'arc_start_angle': -45,
                    'arc_range_angle': 90,
                    'radius': 80 / ratio,
                    'icon_type': 0
                }
            case "RECTANGLE":
                return {
                    'position': position,
                    'width': 100 / ratio,
                    'height': 100 / ratio
                }
            case "POLYGON":
                return {
                    'position': position,
                    'points': []
                }
            case "CIRCLE":
                return {
                    'position': position,
                    'width': 100 / ratio,
                    'height': 100 / ratio
                }
            default:
                return {
                    'position': position,
                    'arc_start_angle': -45,
                    'arc_range_angle': 90,
                    'radius': 80 / ratio,
                    'icon_type': 0
                }
        }
    }

    function updateDialogPos(iconWidth, iconHeight, iconCenterX, iconCenterY) {
        var offset = 10;
        var dialog = configCameraFovLoader.item;
        var dialogW = dialog.width;
        var dialogH = dialog.height;

        // biên của vùng cho phép
        var areaL = _area.x;
        var areaT = _area.y;
        var areaR = _area.x + _area.width;
        var areaB = _area.y + _area.height;

        // toạ độ cạnh của icon
        var iconL = iconCenterX - iconWidth/2;
        var iconR = iconCenterX + iconWidth/2;
        var iconT = iconCenterY - iconHeight/2;
        var iconB = iconCenterY + iconHeight/2;

        // vị trí mặc định: nằm giữa ngang, bên dưới icon
        var x = iconCenterX - dialogW/2;
        var y = iconB + offset;

        // nếu bên dưới không vừa, thử lên trên
        if (y + dialogH > areaB) {
            var yAbove = iconT - offset - dialogH;
            if (yAbove >= areaT) {
                y = yAbove;
            } else {
                // nếu cũng không vừa lên trên, thử bên phải
                var xRight = iconR + offset;
                if (xRight + dialogW <= areaR) {
                    x = xRight;
                    y = iconCenterY - dialogH/2;
                } else {
                    // thử bên trái
                    var xLeft = iconL - offset - dialogW;
                    if (xLeft >= areaL) {
                        x = xLeft;
                        y = iconCenterY - dialogH/2;
                    } else {
                        // fallback: clamp vào vùng
                        x = Math.min(Math.max(x, areaL), areaR - dialogW);
                        y = Math.min(Math.max(y, areaT), areaB - dialogH);
                    }
                }
            }
        }

        // luôn clamp để không vượt biên
        x = Math.min(Math.max(x, areaL), areaR - dialogW);
        y = Math.min(Math.max(y, areaT), areaB - dialogH);

        // chuyển về hệ toạ độ của parent và gán cho dialog
        var finalPos = _rootItem.mapToItem(_2dCameraItem.parent, x, y);
        dialog.x = finalPos.x;
        dialog.y = finalPos.y;
    }

    property color backgroundColor: mapState ? mapState.get_color_theme_by_key("main_background") : "white"
    property color borderColor: mapState ? mapState.get_color_theme_by_key("main_border") : "white"
    property color textColor: mapState ? mapState.get_color_theme_by_key("text_color_all_app") : "white"
    property color primaryColor: mapState ? mapState.get_color_theme_by_key("primary") : "white"
    property string cameraItemIcon: mapState ? mapState.get_image_theme_by_key("camera_item") : "white"

    Connections{
        target: mapState
        function onThemeChanged(){
            backgroundColor = mapState.get_color_theme_by_key("main_background")
            borderColor = mapState.get_color_theme_by_key("main_border")
            textColor = mapState.get_color_theme_by_key("text_color_all_app")
            primaryColor = mapState.get_color_theme_by_key("primary")
            cameraItemIcon = mapState.get_image_theme_by_key("camera_item")
        }
    }
}
